'use client';

import { useCallback, useEffect, useRef, useState } from 'react';

interface DragState {
  isDragging: boolean;
  startY: number;
  startScrollTop: number;
  lastY: number;
  velocity: number;
  lastTime: number;
}

interface UseDragScrollOptions {
  momentum?: boolean;
  momentumDecay?: number;
  dragThreshold?: number;
}

export function useDragScroll(options: UseDragScrollOptions = {}) {
  const { momentum = true, momentumDecay = 0.95, dragThreshold = 3 } = options;

  const scrollRef = useRef<HTMLElement>(null);
  const dragState = useRef<DragState>({
    isDragging: false,
    startY: 0,
    startScrollTop: 0,
    lastY: 0,
    velocity: 0,
    lastTime: 0,
  });

  const [isDragging, setIsDragging] = useState(false);
  const animationFrameRef = useRef<number | null>(null);

  // Momentum animation
  const animateMomentum = useCallback(() => {
    if (!scrollRef.current || !momentum) return;

    const element = scrollRef.current;
    const currentVelocity = dragState.current.velocity;

    if (Math.abs(currentVelocity) < 0.1) {
      dragState.current.velocity = 0;
      return;
    }

    const newScrollTop = element.scrollTop - currentVelocity;
    const maxScroll = element.scrollHeight - element.clientHeight;

    // Clamp scroll position
    const clampedScrollTop = Math.max(0, Math.min(maxScroll, newScrollTop));
    element.scrollTop = clampedScrollTop;

    // Apply decay
    dragState.current.velocity *= momentumDecay;

    // Continue animation
    animationFrameRef.current = requestAnimationFrame(animateMomentum);
  }, [momentum, momentumDecay]);

  // Mouse event handlers
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!scrollRef.current) return;

    // Check if the target is a clickable element (link, button, input, etc.)
    const target = e.target as HTMLElement;
    const isClickableElement = target.closest(
      'a, button, input, select, textarea, [role="button"], [tabindex]'
    );

    // Don't prevent default for clickable elements initially
    if (!isClickableElement) {
      e.preventDefault();
    }

    const currentTime = Date.now();
    dragState.current = {
      isDragging: false, // Start as false, will be set to true when drag threshold is exceeded
      startY: e.clientY,
      startScrollTop: scrollRef.current.scrollTop,
      lastY: e.clientY,
      velocity: 0,
      lastTime: currentTime,
    };

    // Cancel any ongoing momentum animation
    if (animationFrameRef.current !== null) {
      cancelAnimationFrame(animationFrameRef.current);
    }
  }, []);

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!scrollRef.current) return;

      const currentTime = Date.now();
      const deltaY = e.clientY - dragState.current.startY;
      const timeDelta = currentTime - dragState.current.lastTime;

      // Check if we should start dragging (exceeded threshold)
      if (!dragState.current.isDragging && Math.abs(deltaY) > dragThreshold) {
        dragState.current.isDragging = true;
        setIsDragging(true);

        // Prevent default behavior now that we're actually dragging
        e.preventDefault();
      }

      // Only continue if we're actually dragging
      if (!dragState.current.isDragging) return;

      // Calculate velocity for momentum
      if (timeDelta > 0) {
        const velocityDelta = (e.clientY - dragState.current.lastY) / timeDelta;
        dragState.current.velocity = velocityDelta * 10; // Scale for better feel
      }

      const newScrollTop = dragState.current.startScrollTop - deltaY;
      const maxScroll = scrollRef.current.scrollHeight - scrollRef.current.clientHeight;

      // Clamp scroll position
      const clampedScrollTop = Math.max(0, Math.min(maxScroll, newScrollTop));
      scrollRef.current.scrollTop = clampedScrollTop;

      dragState.current.lastY = e.clientY;
      dragState.current.lastTime = currentTime;
    },
    [dragThreshold]
  );

  const handleMouseUp = useCallback(() => {
    // Reset dragging state regardless of whether we were actually dragging
    const wasDragging = dragState.current.isDragging;

    setIsDragging(false);
    dragState.current.isDragging = false;

    // Start momentum animation if enabled and we were actually dragging
    if (wasDragging && momentum && Math.abs(dragState.current.velocity) > 0.1) {
      animationFrameRef.current = requestAnimationFrame(animateMomentum);
    }
  }, [momentum, animateMomentum]);

  // Touch event handlers
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (!scrollRef.current || e.touches.length !== 1) return;

    const touch = e.touches[0];
    if (!touch) return;

    // Don't prevent default to allow other touch interactions initially
    const currentTime = Date.now();
    dragState.current = {
      isDragging: false, // Start as false, will be set to true when drag threshold is exceeded
      startY: touch.clientY,
      startScrollTop: scrollRef.current.scrollTop,
      lastY: touch.clientY,
      velocity: 0,
      lastTime: currentTime,
    };

    // Cancel any ongoing momentum animation
    if (animationFrameRef.current !== null) {
      cancelAnimationFrame(animationFrameRef.current);
    }
  }, []);

  const handleTouchMove = useCallback(
    (e: TouchEvent) => {
      if (!scrollRef.current || e.touches.length !== 1) return;

      const touch = e.touches[0];
      if (!touch) return;

      const currentTime = Date.now();
      const deltaY = touch.clientY - dragState.current.startY;
      const timeDelta = currentTime - dragState.current.lastTime;

      // Check if we should start dragging (exceeded threshold)
      if (!dragState.current.isDragging && Math.abs(deltaY) > dragThreshold) {
        dragState.current.isDragging = true;
        setIsDragging(true);

        // Prevent default behavior now that we're actually dragging
        e.preventDefault();
      }

      // Only continue if we're actually dragging
      if (!dragState.current.isDragging) return;

      // Calculate velocity for momentum
      if (timeDelta > 0) {
        const velocityDelta = (touch.clientY - dragState.current.lastY) / timeDelta;
        dragState.current.velocity = velocityDelta * 10; // Scale for better feel
      }

      const newScrollTop = dragState.current.startScrollTop - deltaY;
      const maxScroll = scrollRef.current.scrollHeight - scrollRef.current.clientHeight;

      // Clamp scroll position
      const clampedScrollTop = Math.max(0, Math.min(maxScroll, newScrollTop));
      scrollRef.current.scrollTop = clampedScrollTop;

      dragState.current.lastY = touch.clientY;
      dragState.current.lastTime = currentTime;
    },
    [dragThreshold]
  );

  const handleTouchEnd = useCallback(() => {
    // Reset dragging state regardless of whether we were actually dragging
    const wasDragging = dragState.current.isDragging;

    setIsDragging(false);
    dragState.current.isDragging = false;

    // Start momentum animation if enabled and we were actually dragging
    if (wasDragging && momentum && Math.abs(dragState.current.velocity) > 0.1) {
      animationFrameRef.current = requestAnimationFrame(animateMomentum);
    }
  }, [momentum, animateMomentum]);

  // Global event listeners
  useEffect(() => {
    const handleGlobalMouseMove = (e: MouseEvent) => handleMouseMove(e);
    const handleGlobalMouseUp = () => handleMouseUp();
    const handleGlobalTouchMove = (e: TouchEvent) => handleTouchMove(e);
    const handleGlobalTouchEnd = () => handleTouchEnd();

    if (isDragging) {
      // Prevent text selection during drag
      document.body.style.userSelect = 'none';
      document.body.style.webkitUserSelect = 'none';

      document.addEventListener('mousemove', handleGlobalMouseMove);
      document.addEventListener('mouseup', handleGlobalMouseUp);
      document.addEventListener('touchmove', handleGlobalTouchMove, { passive: false });
      document.addEventListener('touchend', handleGlobalTouchEnd);
      document.addEventListener('touchcancel', handleGlobalTouchEnd);
    }

    return () => {
      // Restore text selection
      document.body.style.userSelect = '';
      document.body.style.webkitUserSelect = '';

      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
      document.removeEventListener('touchmove', handleGlobalTouchMove);
      document.removeEventListener('touchend', handleGlobalTouchEnd);
      document.removeEventListener('touchcancel', handleGlobalTouchEnd);
    };
  }, [isDragging, handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd]);

  // Cleanup animation frame on unmount
  useEffect(() => {
    return () => {
      if (animationFrameRef.current !== null) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  return {
    scrollRef,
    isDragging,
    dragHandlers: {
      onMouseDown: handleMouseDown,
      onTouchStart: handleTouchStart,
    },
    cursorClass: isDragging ? 'cursor-grabbing' : 'cursor-grab',
  };
}
